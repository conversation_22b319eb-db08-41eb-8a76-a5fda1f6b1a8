import { registerBlockVariation } from '@wordpress/blocks';
import { __ } from '@wordpress/i18n';

wp.hooks.addFilter(
    'woocommerce_blocks_cart_item',
    'custom/cart-item-layout',
    (OriginalCartItem) => {
        return (props) => {
            const { product, cartItem } = props;

            // Get SAP pricing data if available
            const sapPricing = cartItem.extensions && cartItem.extensions.sap_pricing ? cartItem.extensions.sap_pricing : null;

            return (
                <tr className="custom-cart-item">
                    <td className="custom-cart-item__details">
                        <div className="custom-cart-item__image">{product.images[0]}</div>
                        <div className="custom-cart-item__info">
                            <h4>{product.name}</h4>
                            <p>{product.description}</p>
                            <span>{product.prices}</span>

                            {/* SAP Pricing Display */}
                            {sapPricing && (
                                <div className="sap-cart-pricing" style={{
                                    margin: '8px 0',
                                    padding: '8px',
                                    background: '#f8f9fa',
                                    borderRadius: '4px',
                                    borderLeft: '3px solid #007cba'
                                }}>
                                    <div className="sap-pricing-title" style={{
                                        fontWeight: '600',
                                        fontSize: '12px',
                                        color: '#555',
                                        marginBottom: '4px'
                                    }}>
                                        SAP Pricing
                                    </div>
                                    <div className="sap-pricing-values" style={{
                                        display: 'flex',
                                        gap: '15px',
                                        fontSize: '13px'
                                    }}>
                                        {sapPricing.formatted_net_value && (
                                            <div className="sap-pricing-item">
                                                <span className="sap-pricing-label" style={{
                                                    color: '#666',
                                                    fontWeight: '500'
                                                }}>
                                                    Net Price:
                                                </span>
                                                <span className="sap-pricing-value" style={{
                                                    color: '#333',
                                                    fontWeight: '600',
                                                    marginLeft: '4px'
                                                }} dangerouslySetInnerHTML={{
                                                    __html: sapPricing.formatted_net_value
                                                }} />
                                            </div>
                                        )}
                                        {sapPricing.formatted_sales_tax && (
                                            <div className="sap-pricing-item">
                                                <span className="sap-pricing-label" style={{
                                                    color: '#666',
                                                    fontWeight: '500'
                                                }}>
                                                    Sales Tax:
                                                </span>
                                                <span className="sap-pricing-value" style={{
                                                    color: '#333',
                                                    fontWeight: '600',
                                                    marginLeft: '4px'
                                                }} dangerouslySetInnerHTML={{
                                                    __html: sapPricing.formatted_sales_tax
                                                }} />
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    </td>
                    <td className="custom-cart-item__quantity">
                        {cartItem.quantity}
                    </td>
                    <td className="custom-cart-item__subtotal">
                        {cartItem.subtotal}
                    </td>
                    <td className="custom-cart-item__remove">
                        <button
                            onClick={() => props.onRemove(cartItem.key)}
                            aria-label={__('Remove', 'woocommerce')}
                        >
                            ×
                        </button>
                    </td>
                </tr>
            );
        };
    }
);
