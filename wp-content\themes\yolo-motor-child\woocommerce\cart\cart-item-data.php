<?php
/**
 * Cart item data (when outputting non-flat)
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/cart/cart-item-data.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see         https://woocommerce.com/document/template-structure/
 * @package     WooCommerce\Templates
 * @version     2.4.0
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

// Separate SAP pricing data from other item data
$sap_pricing_data = array();
$other_item_data = array();

foreach ( $item_data as $data ) {
	if ( in_array( $data['key'], array( 'Net Price', 'Sales Tax' ) ) ) {
		$sap_pricing_data[] = $data;
	} else {
		$other_item_data[] = $data;
	}
}
?>

<?php if ( ! empty( $sap_pricing_data ) ) : ?>
	<div class="sap-cart-pricing" style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #007cba;">
		<div class="sap-pricing-title" style="font-weight: 600; font-size: 12px; color: #555; margin-bottom: 4px;">SAP Pricing</div>
		<div class="sap-pricing-values" style="display: flex; gap: 15px; font-size: 13px;">
			<?php foreach ( $sap_pricing_data as $data ) : ?>
				<div class="sap-pricing-item">
					<span class="sap-pricing-label" style="color: #666; font-weight: 500;"><?php echo wp_kses_post( $data['key'] ); ?>:</span>
					<span class="sap-pricing-value" style="color: #333; font-weight: 600; margin-left: 4px;"><?php echo wp_kses_post( $data['display'] ); ?></span>
				</div>
			<?php endforeach; ?>
		</div>
	</div>
<?php endif; ?>

<?php if ( ! empty( $other_item_data ) ) : ?>
	<dl class="variation">
		<?php foreach ( $other_item_data as $data ) : ?>
			<dt class="<?php echo sanitize_html_class( 'variation-' . $data['key'] ); ?>"><?php echo wp_kses_post( $data['key'] ); ?>:</dt>
			<dd class="<?php echo sanitize_html_class( 'variation-' . $data['key'] ); ?>"><?php echo wp_kses_post( wpautop( $data['display'] ) ); ?></dd>
		<?php endforeach; ?>
	</dl>
<?php endif; ?>
